package com.snct.service;

import com.snct.hbase.domain.hbase.AwsHbaseVo;
import com.snct.hbase.enums.DeviceTypeEnum;
import com.snct.hbase.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * aws 操作类
 */
@Service
@Transactional(readOnly = true)
public class AwsService {

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    private Logger logger = LoggerFactory.getLogger(AwsService.class);
    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public List<AwsHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.AWS.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);

        return hBaseDaoUtil.scanByRowList(new AwsHbaseVo(), tableName, rowList);
    }

    public AwsHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.AWS.getAlias(), deviceCode, 0);
        return hBaseDaoUtil.getLatestRow(new AwsHbaseVo(), tableName);
    }

    public AwsHbaseVo getLatestDataFromHbase2(String startRowKey, String lastRowKey) {


        String tableName = "snct:aws";
        return hBaseDaoUtil.getLatestRecord(new AwsHbaseVo(), tableName, startRowKey, lastRowKey);
    }

}