package com.snct.hbase.utils;

import com.google.common.collect.Lists;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hbase.HColumnDescriptor;
import org.apache.hadoop.hbase.HTableDescriptor;
import org.apache.hadoop.hbase.NamespaceDescriptor;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.filter.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * @Author: tsohan
 * @Descriptions: HBaseDao操作公共类
 */
@Component("hBaseDaoUtil")
public class HBaseDaoUtil {

    protected final org.slf4j.Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 按天分区的预分区key
     */
    public static final byte[][] DAY_SPLIT_KEYS = new byte[][]{Bytes.toBytes("1"), Bytes.toBytes("2"), Bytes.toBytes(
            "3"), Bytes.toBytes("4"),
            Bytes.toBytes("5"), Bytes.toBytes("6"), Bytes.toBytes("7"), Bytes.toBytes("8"), Bytes.toBytes("9"),
            Bytes.toBytes("10"), Bytes.toBytes("11"), Bytes.toBytes("12"), Bytes.toBytes("13"), Bytes.toBytes("14"),
            Bytes.toBytes("15"), Bytes.toBytes("16"), Bytes.toBytes("17"), Bytes.toBytes("18"), Bytes.toBytes("19"),
            Bytes.toBytes("20"), Bytes.toBytes("21"), Bytes.toBytes("22"), Bytes.toBytes("23"), Bytes.toBytes("24"),
            Bytes.toBytes("25"), Bytes.toBytes("26"), Bytes.toBytes("27"), Bytes.toBytes("28"), Bytes.toBytes("29"),
            Bytes.toBytes("30"), Bytes.toBytes("31")};

    // 关闭连接
    public static void close() {
        if (HConnectionFactory.connection != null) {
            try {
                HConnectionFactory.connection.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @param name
     * @Descripton: 创建命名空间
     * @Author: tsohan
     */
    public void createNameSpace(String name) {
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            // 获取一个namespace的描述器
            NamespaceDescriptor nsd = NamespaceDescriptor.create(name).build();
            admin.createNamespace(nsd);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("创建命名空间失败！", e);
        }
    }

    /**
     * @param name
     * @Descripton: 删除命名空间
     * @Author: tsohan
     */
    public void dropNameSpace(String name) {
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            admin.deleteNamespace(name);
        } catch (IOException e) {
            logger.error("删除命名空间失败！", e);
        }
    }

    public boolean tableExists(String tableName) {
        boolean b = false;
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            TableName[] tableNames = admin.listTableNames();
            for (TableName table : tableNames) {
                if (table.equals(tn)) {
                    b = true;
                }
            }
        } catch (IOException e) {
            logger.error("查询失败！--{}", e);
        }
        return b;
    }

    /**
     * @param tableName
     * @param familyColumn
     * @Descripton: 创建表
     * @Author: tsohan
     */
    public void createTable(String tableName, Set<String> familyColumn) {
        createTable(tableName, familyColumn, null);
    }

    public void createTable(Set<String> tableNames, Set<String> familyColumn, byte[][] splitKeys) {
        for (String tableName : tableNames) {
            createTable(tableName, familyColumn, splitKeys);
        }
    }

    /**
     * @param tableName
     * @param familyColumn
     * @Descripton: 创建表
     * @Author: tsohan
     */
    public void createTable(String tableName, Set<String> familyColumn, byte[][] splitKeys) {
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            HTableDescriptor htd = new HTableDescriptor(tn);
            for (String fc : familyColumn) {
                HColumnDescriptor hcd = new HColumnDescriptor(fc);
                htd.addFamily(hcd);
            }

            if (splitKeys != null) {
                admin.createTable(htd, splitKeys);
            } else {
                admin.createTable(htd);
            }
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("创建" + tableName + "表失败！", e);
        }
    }

    /**
     * 表重命名
     *
     * @param oldName
     * @param newName
     */
    public void tableRename(String oldName, String newName) {
        TableName oldTn = TableName.valueOf(oldName);
        TableName newTn = TableName.valueOf(newName);

        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            admin.disableTable(oldTn);

            // 创建快照
            String snapshot = RandomUtil.randomStr(6);
            admin.snapshot(snapshot, oldTn);

            //克隆到新表
            admin.cloneSnapshot(snapshot, newTn);

            // 删除快照
            admin.deleteSnapshot(snapshot);

            admin.deleteTable(oldTn);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("重命名" + oldName + "表失败！");
        }
    }

    /**
     * @param tableName
     * @Descripton: 删除表
     * @Author: tsohan
     */
    public void dropTable(String tableName) {
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            admin.disableTable(tn);
            admin.deleteTable(tn);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("删除" + tableName + "表失败！");
        }
    }

    /**
     * @param obj
     * @param param
     * @Descripton: 根据条件过滤查询
     * @Author: tsohan
     */
    public <T> List<T> queryScan(T obj, Map<String, String> param) throws Exception {
        List<T> objs = new ArrayList<T>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin();) {
            if (!admin.isTableAvailable(TableName.valueOf(tableName))) {
                return objs;
            }
            Scan scan = new Scan();
            for (Map.Entry<String, String> entry : param.entrySet()) {
                Class<?> clazz = obj.getClass();
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    if (!field.isAnnotationPresent(HBaseColumn.class)) {
                        continue;
                    }
                    field.setAccessible(true);
                    HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
                    String family = orm.family();
                    String qualifier = orm.qualifier();
                    if (qualifier.equals(entry.getKey())) {
                        Filter filter = new SingleColumnValueFilter(Bytes.toBytes(family),
                                Bytes.toBytes(entry.getKey()), CompareFilter.CompareOp.EQUAL,
                                Bytes.toBytes(entry.getValue()));
                        scan.setFilter(filter);
                    }
                }
            }
            ResultScanner scanner = table.getScanner(scan);
            for (Result result : scanner) {
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询失败！");
            throw new Exception(e);
        }
        return objs;
    }

    /**
     * @param obj
     * @param rowkeys
     * @Descripton: 根据rowkey查询
     * @Author: tsohan
     */
    public <T> List<T> get(T obj, String... rowkeys) {
        List<T> objs = new ArrayList<T>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return objs;
        }
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin();) {
            if (!admin.isTableAvailable(TableName.valueOf(tableName))) {
                return objs;
            }
            List<Result> results = getResults(tableName, rowkeys);
            if (results.isEmpty()) {
                return objs;
            }
            for (int i = 0; i < results.size(); i++) {
                T bean = null;
                Result result = results.get(i);
                if (result == null || result.isEmpty()) {
                    continue;
                }
                try {
                    bean = HBaseBeanUtil.resultToBean(result, obj);
                    objs.add(bean);
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("查询异常！", e);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return objs;
    }


    /**
     * @param objs
     * @Descripton: 保存实体对象
     * @Author: tsohan
     */
    public <T> boolean save(T... objs) {
        List<Put> puts = new ArrayList<Put>();
        String tableName = "";
        try (Admin admin = HConnectionFactory.connection.getAdmin();) {
            for (Object obj : objs) {
                if (obj == null) {
                    continue;
                }
                tableName = getORMTable(obj);
                // 表不存在，先获取family创建表
                if (!admin.isTableAvailable(TableName.valueOf(tableName))) {
                    // 获取family, 创建表
                    Class<?> clazz = obj.getClass();
                    Field[] fields = clazz.getDeclaredFields();
                    Set<String> set = new HashSet<>(10);
                    for (int i = 0; i < fields.length; i++) {
                        if (!fields[i].isAnnotationPresent(HBaseColumn.class)) {
                            continue;
                        }
                        fields[i].setAccessible(true);
                        HBaseColumn orm = fields[i].getAnnotation(HBaseColumn.class);
                        String family = orm.family();
                        if ("rowkey".equalsIgnoreCase(family)) {
                            continue;
                        }
                        set.add(family);
                    }
                    // 创建表
                    createTable(tableName, set);
                }
                Put put = HBaseBeanUtil.beanToPut(obj);
                puts.add(put);
            }
        } catch (Exception e) {
            logger.error("保存Hbase异常！--{}", e);
        }
        return savePut(puts, tableName);
    }

    /**
     * @param tableName
     * @param objs
     * @Descripton: 根据tableName保存
     * @Author: tsohan
     */
    public <T> void save(String tableName, T... objs) {
        List<Put> puts = new ArrayList<Put>();
        for (Object obj : objs) {
            if (obj == null) {
                continue;
            }
            try {
                Put put = HBaseBeanUtil.beanToPut(obj);
                puts.add(put);
            } catch (Exception e) {
                logger.error("hbaseDao保存出错---{}", e);
            }
        }
        boolean b = savePut(puts, tableName);
        System.out.println("+++++++++++++++保存成功1+++++++++++++++");
    }

    /**
     * @param obj
     * @param rowkeys
     * @Descripton: 删除
     * @Author: tsohan
     */
    public <T> void delete(T obj, String... rowkeys) {
        String tableName = getORMTable(obj);
        delete(tableName, rowkeys);
    }

    /**
     * @param tableName
     * @param rowkeys
     * @Descripton: 删除
     * @Author: tsohan
     */
    public <T> void delete(String tableName, String... rowkeys) {
        if (StringUtils.isBlank(tableName)) {
            return;
        }
        List<Delete> deletes = new ArrayList<Delete>();
        for (String rowkey : rowkeys) {
            if (StringUtils.isBlank(rowkey)) {
                continue;
            }
            deletes.add(new Delete(Bytes.toBytes(rowkey)));
        }
        delete(deletes, tableName);
    }


    /**
     * @param deletes
     * @param tableName
     * @Descripton: 批量删除
     * @Author: tsohan
     */
    private void delete(List<Delete> deletes, String tableName) {
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));) {
            if (StringUtils.isBlank(tableName)) {
                logger.info("tableName为空！");
                return;
            }
            table.delete(deletes);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("删除失败！", e);
        }
    }

    public void deleteByStartStop(String tableName, long startTime, long endTime) {
        if (StringUtils.isBlank(tableName)) {
            return;
        }
        List<Map<String, String>> rowMapList = getRowListByTime(startTime, endTime);

        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin()) {
            Scan scan;
            List<String> rowkeys = Lists.newArrayList();
            for (Map<String, String> rowMap : rowMapList) {
                scan = new Scan();
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scan.setFilter(new KeyOnlyFilter());
                scanner = table.getScanner(scan);
                for (Result result : scanner) {
                    rowkeys.add(new String(result.getRow()));
                }
            }

            delete(tableName, rowkeys.toArray(new String[rowkeys.size()]));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
    }

    /**
     * @param tableName
     * @Descripton: 根据tableName获取列簇名称
     * @Author: tsohan
     */
    public List<String> familys(String tableName) {
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));) {
            List<String> columns = new ArrayList<String>();
            if (table == null) {
                return columns;
            }
            HTableDescriptor tableDescriptor = table.getTableDescriptor();
            HColumnDescriptor[] columnDescriptors = tableDescriptor.getColumnFamilies();
            for (HColumnDescriptor columnDescriptor : columnDescriptors) {
                String columnName = columnDescriptor.getNameAsString();
                columns.add(columnName);
            }
            return columns;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询列簇名称失败！", e);
        }
        return new ArrayList<String>();
    }

    // 保存方法
    private boolean savePut(List<Put> puts, String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return false;
        }
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin();) {
            table.put(puts);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    // 获取tableName
    private String getORMTable(Object obj) {
        HBaseTable table = obj.getClass().getAnnotation(HBaseTable.class);
        return table.tableName();
    }

    // 获取查询结果
    private List<Result> getResults(String tableName, String... rowkeys) {
        List<Result> resultList = new ArrayList<Result>();
        List<Get> gets = new ArrayList<Get>();
        for (String rowkey : rowkeys) {
            if (StringUtils.isBlank(rowkey)) {
                continue;
            }
            Get get = new Get(Bytes.toBytes(rowkey));
            gets.add(get);
        }
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));) {
            Result[] results = table.get(gets);
            Collections.addAll(resultList, results);
            return resultList;
        } catch (Exception e) {
            e.printStackTrace();
            return resultList;
        }
    }

    /**
     * @param obj
     * @param param
     * @Descripton: 根据条件过滤查询（大于等于）
     * @Author: tsohan
     */
    public <T> List<T> queryScanGreater(T obj, Map<String, String> param) throws Exception {
        List<T> objs = new ArrayList<T>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin();) {
            if (!admin.isTableAvailable(TableName.valueOf(tableName))) {
                return objs;
            }
            Scan scan = new Scan();
            for (Map.Entry<String, String> entry : param.entrySet()) {
                Class<?> clazz = obj.getClass();
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    if (!field.isAnnotationPresent(HBaseColumn.class)) {
                        continue;
                    }
                    field.setAccessible(true);
                    HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
                    String family = orm.family();
                    String qualifier = orm.qualifier();
                    if (qualifier.equals(entry.getKey())) {
                        Filter filter = new SingleColumnValueFilter(Bytes.toBytes(family),
                                Bytes.toBytes(entry.getKey()), CompareFilter.CompareOp.GREATER_OR_EQUAL,
                                Bytes.toBytes(entry.getValue()));
                        scan.setFilter(filter);
                    }
                }
            }
            ResultScanner scanner = table.getScanner(scan);
            for (Result result : scanner) {
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询失败！");
            throw new Exception(e);
        }
        return objs;
    }

    /**
     * 根据rowkey查询记录
     *
     * @param obj
     * @param rowkey
     * @param <T>
     * @return
     */
    public <T> List<T> queryScanRowkey(T obj, String rowkey) {
        List<T> objs = new ArrayList<T>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();
            scan.setRowPrefixFilter(Bytes.toBytes(rowkey));
            scanner = table.getScanner(scan);
            for (Result result : scanner) {
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    public <T> List<T> scanRowkeyList(T obj, List<String> rowkeyList) {
        List<T> objs = new ArrayList<T>();
        String tableName = getORMTable(obj);
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();
            FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ONE);
            RowFilter rowFilter;
            Long sT = System.currentTimeMillis();

            for (String rowkey : rowkeyList) {
                rowFilter = new RowFilter(CompareFilter.CompareOp.EQUAL, new BinaryComparator(Bytes.toBytes(rowkey)));
                filterList.addFilter(rowFilter);
            }

            scan.setFilter(filterList);
            scanner = table.getScanner(scan);

            sT = System.currentTimeMillis();
            for (Result result : scanner) {
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("scanRowkeyList:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("scanRowkeyList:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    /**
     * 根据startRow、endRow查询
     *
     * @param obj
     * @param start startRowKey
     * @param stop  stopRowKey
     * @param <T>
     * @return
     */
    public <T> List<T> scanByStartStop(T obj, String tableName, String start, String stop) {
        List<T> objs = new ArrayList<T>();
        if (StringUtils.isBlank(tableName)) {
            tableName = getORMTable(obj);
        }
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();
            scan.setStartRow(Bytes.toBytes(start));
            scan.setStopRow(Bytes.toBytes(stop));
            scanner = table.getScanner(scan);

            for (Result result : scanner) {
                T beanClone = (T) BeanUtils.cloneBean(HBaseBeanUtil.resultToBean(result, obj));
                objs.add(beanClone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    /**
     * 根据范围查询数据
     *
     * @param obj
     * @param tableName
     * @param rowList
     * @param <T>
     * @return
     */
    public <T> List<T> scanByRowList(T obj, String tableName, List<Map<String, String>> rowList) {
        List<T> objs = Lists.newArrayList();
        if (StringUtils.isBlank(tableName)) {
            return objs;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin()) {
            Scan scan;
            List<Result> resultList = Lists.newArrayList();
            for (Map<String, String> rowMap : rowList) {
                scan = new Scan();
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scanner = table.getScanner(scan);
                for (Result result : scanner) {
                    resultList.add(result);
                }
            }

            objs = HBaseBeanUtil.resultToBean(resultList, obj, objs);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return objs;
    }

    /**
     * 根据范围分页查询
     *
     * @param obj
     * @param tableName
     * @param objs
     * @param rowList
     * @param currentPage
     * @param pageSize
     * @param sort
     * @param <T>
     * @return
     */
    public <T> Integer scanByStartStop4PageDesc(T obj, String tableName, List<T> objs,
                                                List<Map<String, String>> rowList, int currentPage, int pageSize,
                                                String sort) {
        List<byte[]> rows = Lists.newArrayList();
        if (StringUtils.isBlank(tableName)) {
            return 0;
        }

        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin()) {
            int firstIndex = 0;
            if (currentPage > 1) {
                firstIndex = (currentPage - 1) * pageSize;
            }
            int endIndex = currentPage * pageSize;

            Scan scan;
            for (Map<String, String> rowMap : rowList) {
                scan = new Scan();
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scan.setFilter(new KeyOnlyFilter());
                scan.setCaching(5000);
                scanner = table.getScanner(scan);

                int size;
                Result[] results;
                do {
                    results = scanner.next(5000);
                    size = results.length;
                    Arrays.stream(results).forEach(result -> rows.add(result.getRow()));
                } while (size == 5000);
            }
            // list倒序
            if ("descending".equals(sort)) {
                Collections.reverse(rows);
            }

            List<String> getRows = Lists.newArrayList();
            for (int i = firstIndex; i < rows.size(); i++) {
                if (i > endIndex) {
                    break;
                }
                getRows.add(new String(rows.get(i)));
            }

            List<Result> resultList = getResults(tableName, getRows.toArray(new String[getRows.size()]));
            HBaseBeanUtil.resultToBean(resultList, obj, objs);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return rows.size();
    }

    /**
     * 获取最新一条
     *
     * @param obj
     * @param tableName
     * @param <T>
     * @return
     */
    public <T> T getLatestRow(T obj, String tableName) {
        return getLatestRow(obj, tableName, System.currentTimeMillis());
    }

    public <T> T getLatestRow(T obj, String tableName, Long lastTime) {
        return getLatestRow(obj, tableName, lastTime, 90);
    }

    /**
     * 获取时间前最新一条
     *
     * @param obj
     * @param tableName
     * @param <T>
     * @return
     */
    public <T> T getLatestRow(T obj, String tableName, Long lastTime, Integer maxHead) {
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();

            long startTime = DateUtils.addDay(lastTime, -maxHead);
            List<Map<String, String>> rowKeyList = getRowListByTime(startTime, lastTime);

            for (Map<String, String> map : rowKeyList) {
                scan.setStartRow(Bytes.toBytes(map.get("sr")));
                scan.setStopRow(Bytes.toBytes(map.get("er")));
                scanner = table.getScanner(scan);
                Result result = scanner.next();
                if (result != null) {
                    obj = HBaseBeanUtil.resultToBean(result, obj);
                    return obj;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return obj;
    }

    /**
     * 获取表中第一条数据的rowKey
     *
     * @param obj
     * @param tableName
     * @param <T>
     * @return
     */
    public <T> String getFirstRowKey(T obj, String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();

            long endTime = System.currentTimeMillis();
            // 两年的时间长度
            long startTime = DateUtils.addDay(endTime, -(365 * 5));
            List<Map<String, String>> rowKeyList = getRowListByTime(startTime, endTime);
            Collections.reverse(rowKeyList);

            Result lastResult = null;
            int beforeLast = 0;
            for (Map<String, String> map : rowKeyList) {
                scan.setStopRow(Bytes.toBytes(map.get("er")));
                scan.setStartRow(Bytes.toBytes(map.get("sr")));
                scanner = table.getScanner(scan);
                Result result = scanner.next();
                if (result != null) {
                    lastResult = result;
                    beforeLast = 0;
                    continue;
                }
                beforeLast++;
                // 往前查半年都没有数据，则认定为第一条数据
                if (beforeLast > 180) {
                    break;
                }
            }

            if (lastResult != null) {
                return new String(lastResult.getRow());
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return "";
    }

    /**
     * 查询数量
     *
     * @param tableName
     * @param rowList
     * @return
     */
    public Integer queryNumByStartStop(String tableName, List<Map<String, String>> rowList) {
        Integer num = 0;
        if (StringUtils.isBlank(tableName)) {
            return num;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin()) {
            Scan scan;
            for (Map<String, String> rowMap : rowList) {
                scan = new Scan();
                scan.setStopRow(Bytes.toBytes(rowMap.get("er")));
                scan.setStartRow(Bytes.toBytes(rowMap.get("sr")));
                scanner = table.getScanner(scan);
                while (scanner.next() != null) {
                    num++;
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return num;
    }


    public static void main(String[] args) {

        HBaseDaoUtil hbase = new HBaseDaoUtil();
        List<Map<String, String>> list = hbase.getRowListByTime(1743817697000l, 1745891297000l);
        System.out.println("--");
    }

    /**
     * 通过时间范围获取rowKey范围数组
     *
     * @param startTime
     * @param endTime
     */
    public List<Map<String, String>> getRowListByTime(Long startTime, Long endTime) {
        List<Map<String, Long>> timeList = DateUtils.splitDate(startTime, endTime);

        List<Map<String, String>> rowList = Lists.newArrayList();
        Map<String, String> rowMap;

        for (Map<String, Long> tMap : timeList) {
            rowMap = new HashMap<>();

            rowMap.put("sr", getRowKey(tMap.get("st")));
            rowMap.put("er", getRowKey(tMap.get("et")));

            rowList.add(rowMap);
        }

        return rowList;
    }

    public String getRowKey(Long timeStamp) {
        return DateUtils.getDate(timeStamp) + "|" + DateUtils.fetchWholeSecond(timeStamp);
    }

    /**
     * 获取表名
     *
     * @param sn
     * @param typeStr
     * @param deviceCode
     * @param interval
     * @return
     */
    public String getTableName(String sn, String typeStr, String deviceCode, Integer interval) {
        if (interval == 0) {
            return sn + ":" + typeStr + "_" + deviceCode;
        }
        return sn + ":" + typeStr + "_" + deviceCode + "-" + interval;
    }

    public String getTableName(String sn, String typeStr) {
        return sn + ":" + typeStr;
    }

    public String getTableName(String typeStr, String deviceCode, Integer interval) {
        if (interval == 100) {
            return typeStr + "_" + deviceCode + "-all";
        }
        if (interval == 0) {
            return typeStr + "_" + deviceCode;
        }
        return typeStr + "_" + deviceCode + "-" + interval;
    }

    /**
     * 根据多个条件过滤查询Modem数据
     *
     * @param <T>       泛型对象类型
     * @param obj       对象实例
     * @param deviceId  设备ID
     * @param deptId    部门ID
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @return 结果列表
     */
    public <T> List<T> queryModemDataByFilter(T obj, Long deviceId, Long deptId, Long startTime, Long endTime) {
        List<T> resultList = new ArrayList<>();
        String tableName = getORMTable(obj);
        if (org.apache.commons.lang.StringUtils.isBlank(tableName)) {
            return resultList;
        }

        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));
             Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.isTableAvailable(TableName.valueOf(tableName))) {
                return resultList;
            }

            // 获取时间范围内的rowKey范围
            List<Map<String, String>> rowList = getRowListByTime(startTime, endTime);

            Scan scan = new Scan();
            FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ALL);

            // 添加设备ID过滤器
            if (deviceId != null) {
                byte[] family = Bytes.toBytes("i");
                byte[] qualifier = Bytes.toBytes("d_i");
                SingleColumnValueFilter deviceFilter = new SingleColumnValueFilter(
                        family,
                        qualifier,
                        CompareFilter.CompareOp.EQUAL,
                        Bytes.toBytes(deviceId.toString())
                );
                deviceFilter.setFilterIfMissing(true);
                filterList.addFilter(deviceFilter);
            }

            // 添加部门ID过滤器
            if (deptId != null) {
                byte[] family = Bytes.toBytes("i");
                byte[] qualifier = Bytes.toBytes("d_p");
                SingleColumnValueFilter deptFilter = new SingleColumnValueFilter(
                        family,
                        qualifier,
                        CompareFilter.CompareOp.EQUAL,
                        Bytes.toBytes(deptId.toString())
                );
                deptFilter.setFilterIfMissing(true);
                filterList.addFilter(deptFilter);
            }

            // 如果存在过滤条件，应用过滤器
            if (filterList.getFilters().size() > 0) {
                scan.setFilter(filterList);
            }

            // 对每个时间范围执行查询
            for (Map<String, String> rowRange : rowList) {
                scan.setStartRow(Bytes.toBytes(rowRange.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowRange.get("er")));
                scanner = table.getScanner(scan);

                for (Result result : scanner) {
                    try {
                        T bean = HBaseBeanUtil.resultToBean(result, obj);
                        if (bean != null) {
                            resultList.add(bean);
                        }
                    } catch (Exception e) {
                        logger.error("转换结果到对象时发生错误", e);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("根据条件过滤查询数据失败，deviceId: {}, deptId: {}", deviceId, deptId, e);
        } finally {
            if (scanner != null) {
                scanner.close();
            }
        }

        return resultList;
    }

    /**
     * 根据设备ID查询最近一段时间内的数据
     *
     * @param <T>       泛型对象类型
     * @param obj       对象实例
     * @param tableName 表名
     * @param deviceId  设备ID
     * @param days      最近天数
     * @return 结果列表
     */
    public <T> List<T> queryRecentModemData(T obj, String tableName, Long deviceId, int days) {
        List<T> resultList = new ArrayList<>();
        if (org.apache.commons.lang.StringUtils.isBlank(tableName)) {
            return resultList;
        }

        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));
             Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.isTableAvailable(TableName.valueOf(tableName))) {
                return resultList;
            }

            // 获取时间范围
            long endTime = System.currentTimeMillis();
            long startTime = DateUtils.addDay(endTime, -days);

            // 获取时间范围内的rowKey范围
            List<Map<String, String>> rowList = getRowListByTime(startTime, endTime);

            Scan scan = new Scan();
            FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ALL);

            // 添加设备ID过滤器
            if (deviceId != null) {
                byte[] family = Bytes.toBytes("i");
                byte[] qualifier = Bytes.toBytes("d_i");
                SingleColumnValueFilter deviceFilter = new SingleColumnValueFilter(
                        family,
                        qualifier,
                        CompareFilter.CompareOp.EQUAL,
                        Bytes.toBytes(deviceId.toString())
                );
                deviceFilter.setFilterIfMissing(true);
                filterList.addFilter(deviceFilter);
            }

            // 应用过滤器
            scan.setFilter(filterList);

            // 对每个时间范围执行查询
            for (Map<String, String> rowRange : rowList) {
                scan.setStartRow(Bytes.toBytes(rowRange.get("sr")));
                scan.setStopRow(Bytes.toBytes(rowRange.get("er")));
                scanner = table.getScanner(scan);

                for (Result result : scanner) {
                    try {
                        T bean = HBaseBeanUtil.resultToBean(result, obj);
                        if (bean != null) {
                            resultList.add(bean);
                        }
                    } catch (Exception e) {
                        logger.error("转换结果到对象时发生错误", e);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("查询最近Modem数据失败，deviceId: {}, days: {}", deviceId, days, e);
        } finally {
            if (scanner != null) {
                scanner.close();
            }
        }

        return resultList;
    }

    /**
     * 使用PageFilter进行分页查询
     *
     * @param obj       目标对象类型
     * @param tableName 表名
     * @param startRow  起始行键（第一页传null或空字符串，后续页传上一页最后一条记录的行键+"\0"）
     * @param endRow    结束行键
     * @param pageSize  每页大小
     * @param <T>       对象类型
     * @return 查询结果列表
     */
    public <T> List<T> scanWithPageFilter(T obj, String tableName, String startRow, String endRow, int pageSize,
                                          boolean isDesc) {
        List<T> objs = new ArrayList<>();
        if (StringUtils.isBlank(tableName)) {
            tableName = getORMTable(obj);
        }
        if (StringUtils.isBlank(tableName)) {
            return objs;
        }

        logger.info("执行HBase分页查询: 表={}, 起始行={}, 结束行={}, 页大小={}",
                tableName,
                startRow != null ? startRow : "空",
                endRow != null ? endRow : "空",
                pageSize);

        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));
             Admin admin = HConnectionFactory.connection.getAdmin()) {

            // 创建分页过滤器，限制返回记录数
            Filter pageFilter = new PageFilter(pageSize);

            Scan scan = new Scan();

            // 设置倒序查询
            if (isDesc) {
                scan.setReversed(true);
            }

            scan.setFilter(pageFilter);

            // 设置起始行和结束行
            if (startRow != null && !startRow.isEmpty()) {
                scan.setStartRow(Bytes.toBytes(startRow));
            }

            if (endRow != null && !endRow.isEmpty()) {
                //scan.setStopRow(Bytes.toBytes(endRow));
                scan.setFilter(new InclusiveStopFilter(Bytes.toBytes(endRow))); // 包含stopRow
            }

            scan.setCaching(pageSize);

            long startTime = System.currentTimeMillis();
            scanner = table.getScanner(scan);

            for (Result result : scanner) {
                if (result != null && !result.isEmpty()) {
                    try {
                        String rowKey = Bytes.toString(result.getRow());
                        T bean = HBaseBeanUtil.resultToBean(result, obj);
                        // 设置rowKey到id字段
                        if (bean != null) {
                            try {
                                Method setIdMethod = bean.getClass().getMethod("setId", String.class);
                                setIdMethod.invoke(bean, rowKey);
                            } catch (NoSuchMethodException | SecurityException e) {
                                logger.warn("无法设置rowKey到对象ID字段: {}", e.getMessage());
                            }
                            T beanClone = (T) BeanUtils.cloneBean(bean);
                            objs.add(beanClone);
                        }
                    } catch (Exception e) {
                        logger.error("转换HBase结果到对象时出错: {}", e.getMessage(), e);
                    }
                }
            }

            long endTime = System.currentTimeMillis();
            logger.info("HBase分页查询完成: 耗时={}ms, 结果数量={}", (endTime - startTime), objs.size());

        } catch (Exception e) {
            logger.error("HBase分页查询失败: 表={}, 错误={}", tableName, e.getMessage(), e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    logger.error("关闭HBase扫描器时出错: {}", e.getMessage(), e);
                }
            }
        }

        return objs;
    }

    /**
     * 使用FilterList进行过滤扫描
     *
     * @param obj        目标对象类型
     * @param tableName  表名
     * @param startRow   起始行键（如果为null则从表头开始）
     * @param endRow     结束行键（如果为null则到表尾结束）
     * @param filterList 过滤器列表
     * @param pageSize   每页大小
     * @param <T>        对象类型
     * @return 查询结果列表
     */
    public <T> List<T> scanWithFilterList(T obj, String tableName, String startRow, String endRow,
                                          FilterList filterList, int pageSize) {
        List<T> objs = new ArrayList<>();
        if (StringUtils.isBlank(tableName)) {
            tableName = getORMTable(obj);
        }
        if (StringUtils.isBlank(tableName)) {
            return objs;
        }

        logger.info("执行HBase过滤器扫描: 表={}, 起始行={}, 结束行={}, 过滤器={}, 页大小={}",
                tableName,
                startRow != null ? startRow : "表头",
                endRow != null ? endRow : "表尾",
                filterList != null ? filterList.toString() : "空",
                pageSize);

        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));
             Admin admin = HConnectionFactory.connection.getAdmin()) {

            Scan scan = new Scan();

            // 设置过滤器
            if (filterList != null) {
                scan.setFilter(filterList);
            }

            // 设置起始行和结束行
            if (startRow != null && !startRow.isEmpty()) {
                scan.setStartRow(Bytes.toBytes(startRow));
            }

            if (endRow != null && !endRow.isEmpty()) {
                scan.setStopRow(Bytes.toBytes(endRow));
            }

            // 设置分页大小
            scan.setCaching(pageSize);

            long startTime = System.currentTimeMillis();
            scanner = table.getScanner(scan);

            int count = 0;
            for (Result result : scanner) {
                if (result != null && !result.isEmpty()) {
                    try {
                        String rowKey = Bytes.toString(result.getRow());
                        T bean = HBaseBeanUtil.resultToBean(result, obj);
                        // 设置rowKey到id字段
                        if (bean != null) {
                            try {
                                Method setIdMethod = bean.getClass().getMethod("setId", String.class);
                                setIdMethod.invoke(bean, rowKey);
                            } catch (NoSuchMethodException | SecurityException e) {
                                logger.warn("无法设置rowKey到对象ID字段: {}", e.getMessage());
                            }
                            T beanClone = (T) BeanUtils.cloneBean(bean);
                            objs.add(beanClone);
                            count++;

                            // 如果达到分页大小加1，提前结束
                            if (count >= pageSize) {
                                break;
                            }
                        }
                    } catch (Exception e) {
                        logger.error("转换HBase结果到对象时出错: {}", e.getMessage(), e);
                    }
                }
            }

            long endTime = System.currentTimeMillis();
            logger.info("HBase过滤器扫描完成: 耗时={}ms, 结果数量={}/{}",
                    (endTime - startTime), objs.size(), pageSize);

        } catch (Exception e) {
            logger.error("HBase过滤器扫描失败: 表={}, 错误={}", tableName, e.getMessage(), e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    logger.error("关闭HBase扫描器时出错: {}", e.getMessage(), e);
                }
            }
        }

        return objs;
    }

    /**
     * 检查指定表中是否存在指定的列簇
     *
     * @param tableName 表名
     * @param family    列簇名
     * @return 存在返回true，否则返回false
     */
    public boolean columnFamilyExists(String tableName, String family) {
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                return false;
            }

            HTableDescriptor tableDescriptor = admin.getTableDescriptor(tn);
            HColumnDescriptor[] columnFamilies = tableDescriptor.getColumnFamilies();

            for (HColumnDescriptor columnFamily : columnFamilies) {
                if (columnFamily.getNameAsString().equals(family)) {
                    return true;
                }
            }
        } catch (IOException e) {
            logger.error("检查列簇是否存在时出错: {}", e.getMessage(), e);
        }
        return false;
    }

    /**
     * 向已存在的表中添加列簇
     *
     * @param tableName 表名
     * @param family    列簇名
     * @return 添加成功返回true，否则返回false
     */
    public boolean addColumnFamily(String tableName, String family) {
        TableName tn = TableName.valueOf(tableName);
        try (Admin admin = HConnectionFactory.connection.getAdmin()) {
            if (!admin.tableExists(tn)) {
                logger.error("表 {} 不存在，无法添加列簇", tableName);
                return false;
            }

            // 检查列簇是否已存在
            if (columnFamilyExists(tableName, family)) {
                logger.info("列簇 {} 已存在于表 {}", family, tableName);
                return true;
            }

            // 禁用表
            admin.disableTable(tn);

            // 添加列簇
            HColumnDescriptor columnDescriptor = new HColumnDescriptor(family);
            admin.addColumn(tn, columnDescriptor);

            // 启用表
            admin.enableTable(tn);

            logger.info("成功向表 {} 添加列簇 {}", tableName, family);
            return true;
        } catch (IOException e) {
            logger.error("向表 {} 添加列簇 {} 时出错: {}", tableName, family, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存映射的数据到HBase
     *
     * @param tableName 表名
     * @param rowKey    行键
     * @param dataMap   数据映射 (列簇名 -> (列名 -> 值))
     * @return 保存成功返回true，否则返回false
     */
    public boolean saveMappedData(String tableName, String rowKey, Map<String, Map<String, Object>> dataMap) {
        if (StringUtils.isBlank(tableName) || StringUtils.isBlank(rowKey) || dataMap == null || dataMap.isEmpty()) {
            logger.error("保存映射数据参数错误: tableName={}, rowKey={}, dataMap={}", tableName, rowKey, dataMap);
            return false;
        }

        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName))) {
            Put put = new Put(Bytes.toBytes(rowKey));

            // 遍历所有列簇
            for (Map.Entry<String, Map<String, Object>> familyEntry : dataMap.entrySet()) {
                String family = familyEntry.getKey();
                Map<String, Object> columns = familyEntry.getValue();

                if (columns == null || columns.isEmpty()) {
                    continue;
                }

                // 遍历列簇中的所有列
                for (Map.Entry<String, Object> columnEntry : columns.entrySet()) {
                    String qualifier = columnEntry.getKey();
                    Object value = columnEntry.getValue();

                    if (value == null) {
                        continue;
                    }

                    // 根据值的类型添加到Put
                    addValueToPut(put, family, qualifier, value);
                }
            }

            // 执行保存
            table.put(put);
            return true;
        } catch (Exception e) {
            logger.error("保存映射数据到表 {} 失败: {}", tableName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新HBase中指定行的某个列簇数据
     *
     * @param tableName 表名
     * @param rowKey    行键
     * @param family    列簇名
     * @param columns   列数据映射 (列名 -> 值)
     * @return 更新成功返回true，否则返回false
     */
    public boolean updateColumnsInFamily(String tableName, String rowKey, String family, Map<String, Object> columns) {
        if (StringUtils.isBlank(tableName) || StringUtils.isBlank(rowKey) ||
                StringUtils.isBlank(family) || columns == null || columns.isEmpty()) {
            logger.error("更新列簇数据参数错误: tableName={}, rowKey={}, family={}, columns={}",
                    tableName, rowKey, family, columns);
            return false;
        }

        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName))) {
            Put put = new Put(Bytes.toBytes(rowKey));

            // 遍历列簇中的所有列
            for (Map.Entry<String, Object> columnEntry : columns.entrySet()) {
                String qualifier = columnEntry.getKey();
                Object value = columnEntry.getValue();

                if (value == null) {
                    continue;
                }

                // 根据值的类型添加到Put
                addValueToPut(put, family, qualifier, value);
            }

            // 执行更新
            table.put(put);
            return true;
        } catch (Exception e) {
            logger.error("更新表 {} 中行 {} 的列簇 {} 数据失败: {}",
                    tableName, rowKey, family, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据值的类型将其添加到Put对象中
     *
     * @param put       Put对象
     * @param family    列簇名
     * @param qualifier 列名
     * @param value     值
     */
    private void addValueToPut(Put put, String family, String qualifier, Object value) {
        byte[] familyBytes = Bytes.toBytes(family);
        byte[] qualifierBytes = Bytes.toBytes(qualifier);

        if (value instanceof String) {
            put.addColumn(familyBytes, qualifierBytes, Bytes.toBytes((String) value));
        } else if (value instanceof Integer) {
            put.addColumn(familyBytes, qualifierBytes, Bytes.toBytes((Integer) value));
        } else if (value instanceof Long) {
            put.addColumn(familyBytes, qualifierBytes, Bytes.toBytes((Long) value));
        } else if (value instanceof Double) {
            put.addColumn(familyBytes, qualifierBytes, Bytes.toBytes((Double) value));
        } else if (value instanceof Float) {
            put.addColumn(familyBytes, qualifierBytes, Bytes.toBytes((Float) value));
        } else if (value instanceof Boolean) {
            put.addColumn(familyBytes, qualifierBytes, Bytes.toBytes((Boolean) value));
        } else if (value instanceof byte[]) {
            put.addColumn(familyBytes, qualifierBytes, (byte[]) value);
        } else {
            // 对于其他类型，转换为字符串处理
            String strValue = value != null ? value.toString() : "";
            put.addColumn(familyBytes, qualifierBytes, Bytes.toBytes(strValue));
        }
    }

    /**
     * 使用FirstKeyOnlyFilter获取RowKey列表
     *
     * @param obj         目标对象类型
     * @param tableName   表名
     * @param startRow    起始行键（如果为null则从表头开始）
     * @param endRow      结束行键（如果为null则到表尾结束）
     * @param extraFilter 额外的过滤器（可为null）
     * @param caching     每次批量获取的数量
     * @param <T>         对象类型
     * @return RowKey列表
     */
    public <T> List<String> getRowKeyListWithFirstKeyOnlyFilter(T obj, String tableName, String startRow, String endRow,
                                                                Filter extraFilter, boolean isDesc, int caching) {
        List<String> rowKeys = new ArrayList<>();
        if (StringUtils.isBlank(tableName)) {
            tableName = getORMTable(obj);
        }
        if (StringUtils.isBlank(tableName)) {
            return rowKeys;
        }

        logger.info("获取RowKey列表: 表={}, 起始行={}, 结束行={}, 批处理大小={}",
                tableName,
                startRow != null ? startRow : "表头",
                endRow != null ? endRow : "表尾",
                caching);

        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));
             Admin admin = HConnectionFactory.connection.getAdmin()) {

            Scan scan = new Scan();

            // 设置倒序查询
            if (isDesc) {
                scan.setReversed(true);
                // 置换起始行和结束行
                String temp = startRow;
                startRow = endRow;
                endRow = temp;
            }

            // 创建FirstKeyOnlyFilter仅获取每行的第一个KeyValue
            Filter firstKeyOnlyFilter = new FirstKeyOnlyFilter();

            // 如果有额外的过滤器，则与FirstKeyOnlyFilter组合
            if (extraFilter != null) {
                FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ALL);
                filterList.addFilter(firstKeyOnlyFilter);
                filterList.addFilter(extraFilter);
                scan.setFilter(filterList);
            } else {
                scan.setFilter(firstKeyOnlyFilter);
            }

            scan.setMaxVersions(1);

            // 设置批处理大小
            scan.setCaching(caching);

            // 设置起始行和结束行
            if (startRow != null && !startRow.isEmpty()) {
                scan.setStartRow(Bytes.toBytes(startRow));
            }

            if (endRow != null && !endRow.isEmpty()) {
                scan.setStopRow(Bytes.toBytes(endRow));
            }

            long startTime = System.currentTimeMillis();
            scanner = table.getScanner(scan);

            for (Result result : scanner) {
                if (result != null && !result.isEmpty()) {
                    rowKeys.add(Bytes.toString(result.getRow()));
                }
            }

            long endTime = System.currentTimeMillis();
            logger.info("获取RowKey列表完成: 耗时={}ms, 获取RowKey数量={}",
                    (endTime - startTime), rowKeys.size());

        } catch (Exception e) {
            logger.error("获取RowKey列表失败: 表={}, 错误={}", tableName, e.getMessage(), e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    logger.error("关闭HBase扫描器时出错: {}", e.getMessage(), e);
                }
            }
        }

        return rowKeys;
    }

    /**
     * 使用FilterList获取RowKey列表
     *
     * @param obj        目标对象类型
     * @param tableName  表名
     * @param startRow   起始行键（如果为null则从表头开始）
     * @param endRow     结束行键（如果为null则到表尾结束）
     * @param filterList 过滤器列表（不会添加FirstKeyOnlyFilter）
     * @param caching    每次批量获取的数量
     * @param family     要扫描的列族（如果为null则扫描所有列族）
     * @param qualifier  要扫描的列名（如果为null则扫描指定列族的所有列）
     * @param <T>        对象类型
     * @return RowKey列表
     */
    public <T> List<String> getRowKeyListWithFilterList(T obj, String tableName, String startRow, String endRow,
                                                        FilterList filterList, boolean isDesc, int caching,
                                                        String family, String qualifier) {
        List<String> rowKeys = new ArrayList<>();
        if (StringUtils.isBlank(tableName)) {
            tableName = getORMTable(obj);
        }
        if (StringUtils.isBlank(tableName)) {
            return rowKeys;
        }

        logger.info("获取RowKey列表(使用列值过滤): 表={}, 起始行={}, 结束行={}, 批处理大小={}, 列族={}, 列名={}",
                tableName,
                startRow != null ? startRow : "表头",
                endRow != null ? endRow : "表尾",
                caching,
                family != null ? family : "全部",
                qualifier != null ? qualifier : "全部");

        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));
             Admin admin = HConnectionFactory.connection.getAdmin()) {

            Scan scan = new Scan();

            // 设置倒序查询
            if (isDesc) {
                scan.setReversed(true);
                // 置换起始行和结束行
                String temp = startRow;
                startRow = endRow;
                endRow = temp;
            }

            // 直接使用提供的过滤器列表，不添加FirstKeyOnlyFilter
            if (filterList != null) {
                scan.setFilter(filterList);
            }

            // 如果指定了列族和列名，则只扫描特定列
            if (family != null && qualifier != null) {
                scan.addColumn(Bytes.toBytes(family), Bytes.toBytes(qualifier));
            }
            // 如果只指定了列族，则扫描整个列族
            else if (family != null) {
                scan.addFamily(Bytes.toBytes(family));
            }

            // 设置批处理大小
            scan.setCaching(caching);

            // 设置起始行和结束行
            if (startRow != null && !startRow.isEmpty()) {
                scan.setStartRow(Bytes.toBytes(startRow));
            }

            if (endRow != null && !endRow.isEmpty()) {
                scan.setStopRow(Bytes.toBytes(endRow));
            }

            long startTime = System.currentTimeMillis();
            scanner = table.getScanner(scan);

            for (Result result : scanner) {
                if (result != null && !result.isEmpty()) {
                    rowKeys.add(Bytes.toString(result.getRow()));
                }
            }

            long endTime = System.currentTimeMillis();
            logger.info("获取RowKey列表完成(使用列值过滤): 耗时={}ms, 获取RowKey数量={}",
                    (endTime - startTime), rowKeys.size());

        } catch (Exception e) {
            logger.error("获取RowKey列表失败(使用列值过滤): 表={}, 错误={}", tableName, e.getMessage(), e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    logger.error("关闭HBase扫描器时出错: {}", e.getMessage(), e);
                }
            }
        }

        return rowKeys;
    }


    /**
     * 获取表中最新一条记录，使用倒序扫描直接获取最新的一条
     *
     * @param obj       实体对象
     * @param tableName 表名
     * @param <T>       泛型
     * @return 最新的记录
     */
    public <T> T getLatestRecord(T obj, String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));) {
            // 创建倒序扫描器
            Scan scan = new Scan();
            scan.setReversed(true);  // 设置倒序扫描
            scan.setCaching(1);      // 只需要获取一条记录

            scanner = table.getScanner(scan);
            Result result = scanner.next();

            // 如果有结果，直接返回第一条（倒序后的第一条就是最新的）
            if (result != null && !result.isEmpty()) {
                obj = HBaseBeanUtil.resultToBean(result, obj);
                return obj;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("getLatestRecord:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("getLatestRecord:关闭流异常！", e);
                }
            }
        }
        return null;
    }


    public <T> T getLatestRecord(T obj, String tableName, String startRow, String endRow) {
        if (StringUtils.isBlank(tableName)) {
            return null;
        }
        ResultScanner scanner = null;
        try (Table table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName)); Admin admin =
                HConnectionFactory.connection.getAdmin()) {
            Scan scan = new Scan();

            scan.setStartRow(Bytes.toBytes(startRow));
            scan.setStopRow(Bytes.toBytes(endRow));
            scanner = table.getScanner(scan);
            Result result = scanner.next();
            if (result != null) {
                obj = HBaseBeanUtil.resultToBean(result, obj);
                return obj;
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("queryScanRowkey:查询失败！", e);
        } finally {
            if (scanner != null) {
                try {
                    scanner.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("queryScan:关闭流异常！", e);
                }
            }
        }
        return obj;
    }

    /**
     * 构建用于前缀过滤的key
     *
     * @param deptId   部门ID
     * @param sn       船舶SN
     * @param deviceId 设备ID
     * @param isAdmin  是否管理员
     * @return 前缀键
     */
    public String buildPrefixKey(Long deptId, String sn, Long deviceId, boolean isAdmin) {
        StringBuilder prefix = new StringBuilder();

        // 未指定部门ID且非管理员，无法构建有效前缀
        if (deptId == null && !isAdmin) {
            return "";
        }

        // 指定了部门ID
        if (deptId != null) {
            prefix.append(String.format("%05d", deptId));

            // 如果还指定了船舶SN
            if (sn != null && !sn.isEmpty()) {
                prefix.append("_").append(sn);

                // 如果还指定了设备ID
                if (deviceId != null) {
                    prefix.append("_").append(String.format("%05d", deviceId));
                }
            }
        }

        return prefix.toString();
    }


    /**
     * 构建起始RowKey
     * 根据查询设计文档实现各种条件查询
     *
     * @param deptId    部门ID
     * @param sn        船舶SN
     * @param deviceId  设备ID
     * @param startDate 开始日期
     * @param isAdmin   是否为管理员
     * @return 起始RowKey
     */
    public String buildStartRowKey(Long deptId, String sn, Long deviceId, String startDate, boolean isAdmin) {
        StringBuilder builder = new StringBuilder();

        // 管理员可以查询所有数据，非管理员必须指定部门
        if (isAdmin && deptId == null) {
            // 管理员查询所有部门的情况
            builder.append("00000");
        } else {
            // 必须有部门ID
            Long effectiveDeptId = (deptId != null) ? deptId : 0L;
            builder.append(String.format("%05d", effectiveDeptId));
        }

        // 添加船舶SN
        builder.append("_");
        if (sn != null && !sn.isEmpty()) {
            builder.append(sn);
        } else {
            // 未指定船舶SN，使用起始值
            builder.append("000000");
        }

        // 添加设备ID
        builder.append("_");
        if (deviceId != null) {
            builder.append(String.format("%05d", deviceId));
        } else {
            // 未指定设备ID，使用起始值
            builder.append("00000");
        }

        // 添加时间范围
        builder.append("_");
        if (startDate != null && !startDate.isEmpty()) {
            // 检查startDate是否已经包含时间部分
            if (startDate.contains("_")) {
                // 已经包含日期和时间部分，直接使用
                builder.append(startDate);
            } else {
                // 只包含日期部分，添加默认的起始时间
                builder.append(startDate).append("_000000");
            }
        } else {
            // 未指定开始日期，从最早的数据开始
            builder.append("00000000_000000");
        }

        return builder.toString();
    }

    /**
     * 构建结束RowKey
     * 根据查询设计文档实现各种条件查询
     *
     * @param deptId   部门ID
     * @param sn       船舶SN
     * @param deviceId 设备ID
     * @param endDate  结束日期
     * @param isAdmin  是否为管理员
     * @return 结束RowKey
     */
    public String buildEndRowKey(Long deptId, String sn, Long deviceId, String endDate, boolean isAdmin) {
        StringBuilder builder = new StringBuilder();

        // 管理员可以查询所有数据，非管理员必须指定部门
        if (isAdmin && deptId == null) {
            // 管理员查询所有部门的情况
            builder.append("99999");
        } else {
            // 必须有部门ID
            Long effectiveDeptId = (deptId != null) ? deptId : 0L;
            builder.append(String.format("%05d", effectiveDeptId));
        }

        // 添加船舶SN
        builder.append("_");
        if (sn != null && !sn.isEmpty()) {
            builder.append(sn);
        } else {
            // 未指定船舶SN，使用结束值
            builder.append("zzzzzz");
        }

        // 添加设备ID
        builder.append("_");
        if (deviceId != null) {
            builder.append(String.format("%05d", deviceId));
        } else {
            // 未指定设备ID，使用结束值
            builder.append("99999");
        }

        // 添加时间范围
        builder.append("_");
        if (endDate != null && !endDate.isEmpty()) {
            // 检查endDate是否已经包含时间部分
            if (endDate.contains("_")) {
                // 已经包含日期和时间部分，直接使用
                builder.append(endDate);
            } else {
                // 只包含日期部分，添加默认的结束时间
                builder.append(endDate).append("_235959");
            }
        } else {
            // 未指定结束日期，到最新的数据为止
            builder.append("99999999_999999");
        }

        return builder.toString();
    }
}
